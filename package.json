{"scripts": {"dev": "npx nodemon"}, "type": "module", "dependencies": {"@prisma/client": "^6.11.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^5.1.0", "firebase-admin": "^13.4.0", "jsonwebtoken": "^9.0.2", "node-appwrite": "^17.0.0", "nodemon": "^3.1.10", "ts-lib": "^0.0.5", "uninstall": "^0.0.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "jest": "^30.0.2", "prisma": "^6.11.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}