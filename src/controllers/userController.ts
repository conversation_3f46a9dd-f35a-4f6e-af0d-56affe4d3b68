import express from 'express';
import bcrypt from 'bcrypt';
import prisma from '../prisma';

export const getUserProfile = async (req: express.Request, res: express.Response) => {
  const userId = req.user?.id;

  if (!userId) {
    res.status(401).json({ message: 'User ID not found in token' });
    return;
  }

  try {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        id: true,
        email: true,
        name: true,
        googleId: true, // ← Thêm để check Google account
        password: true, // ← Thêm để check hasPassword
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Format response để match với frontend
    const userProfile = {
      id: user.id,
      email: user.email,
      name: user.name,
      googleId: user.googleId,
      hasPassword: !!user.password, // Convert to boolean
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    res.status(200).json({ user: userProfile });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

export const setPassword = async (req: express.Request, res: express.Response) => {
  const userId = req.user?.id;

  if (!userId) {
    res.status(401).json({ message: 'User ID not found in token' });
    return;
  }

  const { password } = req.body;

  if (!password || password.length < 6) {
    res.status(400).json({ message: 'Password must be at least 6 characters' });
    return;
  }

  try {
    const hashedPassword = await bcrypt.hash(password, 10);

    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    res.status(200).json({ message: 'Password set successfully' });
  } catch (error) {
    console.error('Error setting password:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};
