import { sign, verify } from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required');
}

interface JwtPayLoad {
  userId: number;
  email: string;
}

export const generateToken = (payload: JwtPayLoad): string => {
  return sign(payload, JWT_SECRET, { expiresIn: '1h' });
};

export const verifyToken = (token: string): JwtPayLoad | null => {
  try {
    const decoded = verify(token, JWT_SECRET as string) as JwtPayLoad;
    return decoded;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
};
