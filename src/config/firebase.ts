import * as admin from 'firebase-admin';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH
  ? path.resolve(__dirname, '../../', process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH)
  : undefined;

if (!serviceAccountPath) {
  console.error('FIREBASE_SERVICE_ACCOUNT_KEY_PATH is not set in .env');
  process.exit(1);
}

try {
  const serviceAccount = require(serviceAccountPath);

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });

  
} catch (error) {
  console.error('Error initializing Firebase:', error);
  process.exit(1);
}
export default admin;
