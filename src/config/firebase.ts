import admin from 'firebase-admin';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { promises as fs } from 'fs';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH
  ? path.resolve(__dirname, '../../', process.env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH)
  : undefined;

async function initializeFirebase() {
  if (!serviceAccountPath) {
    console.error('FIREBASE_SERVICE_ACCOUNT_KEY_PATH is not set in .env');
    process.exit(1);
  }

  try {
    console.log('Attempting to read Firebase service account from:', serviceAccountPath);
    const serviceAccountContent = await fs.readFile(serviceAccountPath, 'utf8');
    console.log('Service account file read successfully');

    const serviceAccount = JSON.parse(serviceAccountContent);
    console.log('Service account JSON parsed successfully');

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });

    console.log('Firebase initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    console.error('Service account path:', serviceAccountPath);
    process.exit(1);
  }
}

initializeFirebase();

export default admin;
