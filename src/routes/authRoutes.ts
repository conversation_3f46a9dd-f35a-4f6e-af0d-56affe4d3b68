import { Router } from 'express';
import { register, login, logout, googleLogin, testAuth } from '../controllers/authController';
import { authToken } from '../middlewares/authMiddleware';

const router = Router();

router.post('/register', register);
router.post('/login', login);
router.post('/google-login', googleLogin);
router.post('/logout', logout);
router.get('/test', authToken, testAuth); // Protected route để test authentication

export default router;
