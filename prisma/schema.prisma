// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int @id @default(autoincrement())
  email     String @unique
  name      String
  password  String?
  googleId  String? @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("users")

  contents Content[]
  channels Channel[]
}

model Content {
  id        Int @id @default(autoincrement())
  title     String 
  body      String @db.Text
  status    String @default("draft") @db.VarChar(100)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  userId Int
  user User @relation(fields: [userId], references: [id])

  scheduledPosts ScheduledPost[]
  
  @@map("contents")
}

model Channel {
  id            Int @id @default(autoincrement())
  name          String 
  platformType  String @db.VarChar(100)
  credentials   String @db.Text
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  userId Int
  user User @relation(fields: [userId], references: [id])

  scheduledPosts ScheduledPost[]
  
  @@map("channels")
}

model ScheduledPost {
  id           Int @id @default(autoincrement())
  publishedAt  DateTime
  status       String @default("pending") @db.VarChar(100)
  errorMessage String? @db.Text
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  contentId Int
  content   Content @relation(fields: [contentId], references: [id])

  channelId Int
  channel   Channel @relation(fields: [channelId], references: [id])

  @@map("scheduled_posts")
}
